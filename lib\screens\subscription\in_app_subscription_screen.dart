/// 바라 부스 매니저 - 인앱 구독 관리 화면
///
/// iOS App Store와 Google Play Store의 인앱 구독을 관리하는 화면입니다.
/// - 구독 상태 확인
/// - 플러스 플랜 구독/취소
/// - 구독 복원
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 1월
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import '../../services/in_app_purchase_service.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/app_bar_styles.dart';
import '../../utils/app_colors.dart';
import '../../models/subscription_plan.dart';
import '../../providers/subscription_provider.dart';
import '../../utils/currency_utils.dart';
import '../../services/subscription_service.dart';
import '../../widgets/phone_verification_widget.dart';
import '../../config/app_config.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'subscription_plans_screen.dart';

/// 인앱 구독 관리 화면
class InAppSubscriptionScreen extends ConsumerStatefulWidget {
  const InAppSubscriptionScreen({super.key});

  @override
  ConsumerState<InAppSubscriptionScreen> createState() => _InAppSubscriptionScreenState();
}

class _InAppSubscriptionScreenState extends ConsumerState<InAppSubscriptionScreen> {
  static const String _tag = 'InAppSubscriptionScreen';
  
  final InAppPurchaseService _purchaseService = InAppPurchaseService();
  
  bool _isLoading = false;
  List<ProductDetails> _products = [];
  List<PurchaseDetails> _activeSubscriptions = [];
  UserSubscription? _currentSubscription;
  bool _isPhoneVerified = false;
  String? _verifiedPhoneNumber;

  @override
  void initState() {
    super.initState();
    _initializeService();
    // 서버 동기화는 필요할 때만 수동으로 실행
  }

  @override
  void dispose() {
    _purchaseService.dispose();
    super.dispose();
  }

  /// 서비스 초기화 및 데이터 로드
  Future<void> _initializeService() async {
    setState(() => _isLoading = true);

    try {
      // InAppPurchaseService에 Ref 설정 (Provider 갱신을 위해)
      _purchaseService.setRef(ref);
      await _purchaseService.initialize();
      await _loadData();
    } catch (e) {
      LoggerUtils.logError('서비스 초기화 실패', tag: _tag, error: e);
      ToastUtils.showError(context, '서비스 초기화에 실패했습니다.');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 데이터 로드
  Future<void> _loadData() async {
    try {
      // 현재 구독 상태 조회 (기존 구독 시스템)
      final subscriptionService = SubscriptionService();
      final currentSubscription = await subscriptionService.getCurrentSubscription();

      final results = await Future.wait([
        _purchaseService.getAvailableProducts(),
        _purchaseService.getActiveSubscriptions(),
        _checkPhoneVerification(),
      ]);

      setState(() {
        _products = results[0] as List<ProductDetails>;
        _activeSubscriptions = results[1] as List<PurchaseDetails>;
        _currentSubscription = currentSubscription;
      });

      LoggerUtils.logInfo('데이터 로드 완료 - 상품: ${_products.length}, 구독: ${_activeSubscriptions.length}, 현재 구독: ${currentSubscription?.planType}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('데이터 로드 실패', tag: _tag, error: e);
    }
  }

  /// 전화번호 인증 상태 확인
  Future<void> _checkPhoneVerification() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        if (mounted) setState(() => _isPhoneVerified = false);
        return;
      }

      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      final phoneVerified = userDoc.data()?['phoneVerified'] as bool? ?? false;
      final phoneNumber = userDoc.data()?['phone'] as String?;

      if (mounted) {
        setState(() {
          _isPhoneVerified = phoneVerified;
          _verifiedPhoneNumber = phoneVerified ? phoneNumber : null;
        });
      }
    } catch (e) {
      LoggerUtils.logError('전화번호 인증 상태 확인 실패', tag: _tag, error: e);
      if (mounted) {
        setState(() => _isPhoneVerified = false);
      }
    }
  }



  /// 전화번호 인증 상태 재확인
  Future<void> _recheckPhoneVerification() async {
    setState(() => _isLoading = true);

    try {
      await _checkPhoneVerification();

      if (!_isPhoneVerified) {
        ToastUtils.showWarning(context, '전화번호 인증이 필요합니다.');
      } else {
        ToastUtils.showSuccess(context, '전화번호 인증이 확인되었습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('전화번호 인증 재확인 실패', tag: _tag, error: e);
      ToastUtils.showError(context, '인증 상태 확인 중 오류가 발생했습니다.');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 전화번호 인증 필요 알림
  void _showPhoneVerificationRequired() {
    ToastUtils.showWarning(context, '구독을 위해 전화번호 인증이 필요합니다.');

    // 전화번호 인증 섹션으로 스크롤 (선택사항)
    // 여기서는 간단히 토스트만 표시
  }

  /// 플랜 비교 페이지로 이동
  void _navigateToPlanComparison() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SubscriptionPlansScreen(),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx) => Text('구독 관리', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 현재 구독 상태 헤더 (깔끔한 디자인)
                      _buildSubscriptionHeader(),
                      const SizedBox(height: 20),

                      // 구독 플랜 섹션 (개선된 디자인)
                      _buildPlusPlansSection(),
                      const SizedBox(height: 20),

                      // 전화번호 인증 섹션 (항상 표시, 상태에 따라 다르게)
                      _buildPhoneVerificationSection(),
                      const SizedBox(height: 20),

                      // 구독 관리 섹션
                      _buildManagementSection(),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  /// 구독 상태 헤더 (Netflix/Spotify 스타일의 깔끔한 디자인)
  Widget _buildSubscriptionHeader() {
    final currentPlanAsync = ref.watch(currentPlanTypeProvider);
    final currentSubscriptionAsync = ref.watch(currentSubscriptionProvider);

    return currentPlanAsync.when(
      data: (currentPlanType) {
        return currentSubscriptionAsync.when(
          data: (subscription) {
            final isActive = subscription?.isValid ?? false;

            // 플랜 정보 설정
            String planName;
            String planDescription;
            IconData planIcon;

            // 실제 구독 상태에 따라 표시
            if (isActive && currentPlanType == SubscriptionPlanType.plus) {
              planName = 'PLUS 플랜';
              planDescription = '무제한 행사 • 무제한 상품 • 고급 기능';
              planIcon = Icons.star;
            } else {
              planName = '무료 플랜';
              planDescription = '행사 1개 • 상품 30개 제한';
              planIcon = Icons.free_breakfast;
            }

            return Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                // 더 가시성 좋은 파스텔톤 파란색 배경 적용
                color: (isActive && currentPlanType == SubscriptionPlanType.plus)
                    ? const Color(0xFFE3F2FD) // Material Blue 50보다 조금 더 진한 파스텔 블루
                    : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(16),
                // 모든 경우에 테두리 적용
                border: Border.all(
                  color: (isActive && currentPlanType == SubscriptionPlanType.plus)
                      ? const Color(0xFF90CAF9) // Material Blue 200으로 더 선명한 테두리
                      : Colors.grey.shade300,
                  width: 1.5,
                ),
                // 그림자 효과 유지하되 더 부드럽게
                boxShadow: (isActive && currentPlanType == SubscriptionPlanType.plus)
                    ? [
                        BoxShadow(
                          color: const Color(0xFF2196F3).withOpacity(0.2), // Material Blue 500 기반 그림자
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                      ]
                    : [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                      ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        planIcon,
                        color: (isActive && currentPlanType == SubscriptionPlanType.plus)
                            ? const Color(0xFF1976D2) // Material Blue 700으로 더 진한 파란색
                            : Colors.grey.shade600,
                        size: 28
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              planName,
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: (isActive && currentPlanType == SubscriptionPlanType.plus)
                                    ? const Color(0xFF0D47A1) // Material Blue 900으로 더 진한 파란색
                                    : Colors.grey.shade800,
                              ),
                            ),
                            Text(
                              planDescription,
                              style: TextStyle(
                                fontSize: 14,
                                color: (isActive && currentPlanType == SubscriptionPlanType.plus)
                                    ? const Color(0xFF1565C0) // Material Blue 800으로 적절한 대비
                                    : Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (isActive && subscription != null) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.schedule,
                            color: (isActive && currentPlanType == SubscriptionPlanType.plus)
                                ? Colors.black
                                : Colors.grey.shade600,
                            size: 16
                          ),
                          const SizedBox(width: 8),
                          Text(
                            subscription.subscriptionEndDate != null
                                ? '${subscription.subscriptionEndDate!.year}년 ${subscription.subscriptionEndDate!.month}월 ${subscription.subscriptionEndDate!.day}일까지'
                                : '무제한',
                            style: TextStyle(
                              color: (isActive && currentPlanType == SubscriptionPlanType.plus)
                                  ? Colors.black
                                  : Colors.grey.shade600,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            );
          },
          loading: () => _buildLoadingCard(),
          error: (error, stack) => _buildErrorCard(),
        );
      },
      loading: () => _buildLoadingCard(),
      error: (error, stack) => _buildErrorCard(),
    );
  }

  /// 로딩 카드
  Widget _buildLoadingCard() {
    return Container(
      width: double.infinity,
      height: 120,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
      ),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  /// 에러 카드
  Widget _buildErrorCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        children: [
          Icon(Icons.error, color: Colors.red.shade600, size: 32),
          const SizedBox(height: 8),
          Text(
            '구독 상태를 불러올 수 없습니다',
            style: TextStyle(color: Colors.red.shade700),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () => ref.refresh(subscriptionNotifierProvider),
            child: const Text('다시 시도'),
          ),
        ],
      ),
    );
  }

  /// 전화번호 인증 섹션
  Widget _buildPhoneVerificationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '본인 인증',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),

        if (_isPhoneVerified)
          // 인증 완료 상태 (차분한 디자인)
          Card(
            elevation: 1,
            child: ListTile(
              leading: Icon(Icons.verified, color: Colors.green.shade600),
              title: const Text(
                '전화번호 인증 완료',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),
              subtitle: Text(_verifiedPhoneNumber ?? '인증된 전화번호'),
              trailing: TextButton(
                onPressed: () => _recheckPhoneVerification(),
                child: const Text('재확인'),
              ),
            ),
          )
        else
          // 인증 필요 상태
          PhoneVerificationWidget(
            isCompact: true,
            onVerificationComplete: () {
              _checkPhoneVerification(); // 전화번호 정보 포함해서 재확인
              _loadData(); // 데이터 새로고침
            },
          ),
      ],
    );
  }

  /// 플러스 플랜 섹션 (개선된 디자인)
  Widget _buildPlusPlansSection() {
    final currentPlanAsync = ref.watch(currentPlanTypeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '구독 플랜',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        currentPlanAsync.when(
          data: (currentPlanType) {
            final isCurrentlyPlus = currentPlanType == SubscriptionPlanType.plus;

            return _buildPlanCard(
              title: 'PLUS 플랜',
              price: '${CurrencyUtils.formatWon(2900)}',
              period: '월',
              features: [
                '무제한 행사 생성',
                '무제한 상품 등록',
                '고급 통계 기능',
                '엑셀/PDF 내보내기',
                '판매자별 관리',
              ],
              isRecommended: false,
              isCurrentPlan: isCurrentlyPlus,
              isPhoneVerified: _isPhoneVerified,
              onTap: isCurrentlyPlus ? null : (_isPhoneVerified ? () => _subscribeToPlan() : () => _showPhoneVerificationRequired()),
            );
          },
          loading: () => const SizedBox(height: 200, child: Center(child: CircularProgressIndicator())),
          error: (error, stack) => const SizedBox(height: 100, child: Center(child: Text('플랜 정보를 불러올 수 없습니다'))),
        ),
      ],
    );
  }

  /// 플랜 카드 (Netflix/Spotify 스타일)
  Widget _buildPlanCard({
    required String title,
    required String price,
    required String period,
    required List<String> features,
    required bool isRecommended,
    required bool isCurrentPlan,
    required bool isPhoneVerified,
    VoidCallback? onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.primarySeed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primarySeed.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primarySeed.withOpacity(0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 헤더
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                title,
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primarySeed,
                                ),
                              ),
                              const Spacer(),
                              TextButton.icon(
                                onPressed: _navigateToPlanComparison,
                                icon: Icon(Icons.compare_arrows, size: 14, color: AppColors.primarySeed),
                                label: Text(
                                  '플랜 비교하기 ->',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: AppColors.primarySeed,
                                  ),
                                ),
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  minimumSize: Size.zero,
                                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                price,
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primarySeed,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '/ $period',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppColors.primarySeed.withOpacity(0.7),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                  ],
                ),

                const SizedBox(height: 16),

                // 기능 목록
                ...features.map((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: AppColors.primarySeed,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        feature,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.onSurface,
                        ),
                      ),
                    ],
                  ),
                )),

                if (onTap != null) ...[
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: onTap,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primarySeed,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        isCurrentPlan
                            ? '현재 사용 중'
                            : (isPhoneVerified ? '구독하기' : '전화번호 인증 필요'),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 플랜 구독 메서드
  Future<void> _subscribeToPlan() async {
    // 전화번호 인증 확인
    if (AppConfig.PHONE_VERIFICATION_REQUIRED && !_isPhoneVerified) {
      ToastUtils.showWarning(context, '구독을 위해 전화번호 인증이 필요합니다.');
      return;
    }

    // 기존 구독 로직 실행
    await _purchasePlusSubscription();
  }

  /// 기존 구독 로직 (기존 메서드 재사용)
  Future<void> _purchasePlusSubscription() async {
    await _subscribeToPlusPlan();
  }



  /// 관리 섹션
  Widget _buildManagementSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '구독 관리',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Card(
          elevation: 2,
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.restore, color: Colors.green),
                title: const Text('구독 복원'),
                subtitle: const Text('이전에 구매한 구독을 복원합니다'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: _restorePurchases,
              ),

            ],
          ),
        ),
      ],
    );
  }

  /// 플러스 플랜 구독
  Future<void> _subscribeToPlusPlan() async {
    // 중복 구독 방지 체크
    if (_currentSubscription?.planType == SubscriptionPlanType.plus &&
        _currentSubscription?.isActive == true) {
      ToastUtils.showWarning(context, '이미 플러스 플랜을 구독 중입니다.');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final success = await _purchaseService.subscribeToPlusPlan();
      if (success) {
        ToastUtils.showSuccess(context, '구독 요청이 처리되었습니다.');
        await _loadData();
        ref.invalidate(subscriptionNotifierProvider);
      } else {
        ToastUtils.showError(context, '구독 요청에 실패했습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('구독 요청 실패', tag: _tag, error: e);
      ToastUtils.showError(context, '구독 요청 중 오류가 발생했습니다.');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 구독 복원
  Future<void> _restorePurchases() async {
    setState(() => _isLoading = true);
    
    try {
      await _purchaseService.restorePurchases();
      ToastUtils.showSuccess(context, '구독 복원이 완료되었습니다.');
      await _loadData();
      ref.invalidate(subscriptionNotifierProvider);
    } catch (e) {
      LoggerUtils.logError('구독 복원 실패', tag: _tag, error: e);
      ToastUtils.showError(context, '구독 복원 중 오류가 발생했습니다.');
    } finally {
      setState(() => _isLoading = false);
    }
  }




}
