// The following Firebase imports have been removed as they are not present in the original file.
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:dropdown_button2/dropdown_button2.dart';

import '../../models/prepayment_sort_order.dart';
import '../../providers/prepayment_provider.dart';
import '../../models/event_workspace.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../providers/settings_provider.dart';
import '../../providers/prepayment_tab_provider.dart';

import '../../utils/orientation_helper.dart';
import '../../utils/error_utils.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/app_colors.dart';
import '../prepayment/register_prepayment_screen.dart';
import '../excel/excel_import_screen.dart';
import 'qr_scan_screen.dart';
// 로컬 전용 모드: 실시간 동기화 관련 import 제거됨

import '../../widgets/modern_dialog_components.dart';
import '../../providers/prepayment_state.dart';
import '../../utils/subscription_utils.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;

import '../prepayment/prepayment_virtual_product_management_screen.dart';
import '../prepayment/prepayment_screen.dart';

class InventoryScreen extends ConsumerStatefulWidget {
  const InventoryScreen({super.key});

  @override
  ConsumerState<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends ConsumerState<InventoryScreen>
    with RestorationMixin {
  static const String _tag = 'PrepaymentScreen';
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // 검색 모드 관련 변수들
  bool _isSearchMode = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  String? get restorationId => 'inventory_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: _tag);

    // 모든 방향 허용 (인벤토리 페이지부터는 가로모드 허용)
    OrientationHelper.enterAllOrientationsMode();

    // 초기 데이터 로드 (한 번만 실행)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
      _initializeRealtimeSync();
    });

    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  /// 완전한 메모리 정리 - 행사 전환 시 크래시 방지 (파일은 보존)
  Future<void> _performCompleteMemoryCleanup() async {
    try {
      LoggerUtils.methodStart('_performCompleteMemoryCleanup', tag: _tag);

      // 이미지 캐시 정리 (메모리만, 파일은 보존)
      imageCache.clear();
      imageCache.clearLiveImages();

      // 네트워크 이미지 캐시 정리 (cached_network_image 플러그인)
      final cacheManager = DefaultCacheManager();
      await cacheManager.emptyCache();

      // 파일 삭제 호출 제거 - 사용자 데이터 보존
      // await cleanupAppResources(); // 제거: 이미지 파일 삭제 방지

      // GC 강제 실행 (개발 모드에서만)
      if (kDebugMode) {
        LoggerUtils.logInfo('메모리 정리 완료 - 강제 GC 요청 (파일은 보존)', tag: _tag);
      }

      LoggerUtils.methodEnd('_performCompleteMemoryCleanup', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('메모리 정리 중 오류: $e', tag: _tag);
    }
  }

  /// 이벤트 변경 후 데이터 새로고침
  Future<void> _refreshDataAfterEventChange() async {
    if (!mounted) return;

    try {
      LoggerUtils.methodStart('_refreshDataAfterEventChange', tag: _tag);

      // 강력한 메모리 정리 - 행사 전환 시 크래시 방지
      await _performCompleteMemoryCleanup();

      // 실시간 동기화가 활성화되어 있으므로 에러 상태만 클리어
      final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);

      // 에러 상태 클리어 (데이터는 실시간 동기화로 자동 갱신됨)
      prepaymentNotifier.clearError();

      LoggerUtils.logInfo('행사 전환 - 메모리 정리 및 상태 클리어 완료', tag: _tag);
      LoggerUtils.methodEnd('_refreshDataAfterEventChange', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('이벤트 변경 후 상태 클리어 실패', tag: _tag, error: e);
    }
  }

  /// 실시간 동기화 초기화 (중복 초기화 제거)
  Future<void> _initializeRealtimeSync() async {
    try {
      LoggerUtils.methodStart('_initializeRealtimeSync', tag: _tag);
      // 실시간 동기화 서비스 접근 (초기화는 app_wrapper에서 수행됨)
      // 로컬 전용 모드: 실시간 동기화 서비스 제거됨

      // 로컬 전용 모드: 실시간 구독 제거됨
      LoggerUtils.logInfo('로컬 전용 모드: 실시간 구독 건너뜀', tag: _tag);
      
  LoggerUtils.logInfo('실시간 동기화 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('실시간 동기화 초기화 실패', tag: _tag, error: e);
      // 실패해도 앱 동작에는 영향을 주지 않음
    }
  }

  @override
  void dispose() {
    LoggerUtils.methodStart('dispose', tag: _tag);

    _searchController.dispose();

    LoggerUtils.methodEnd('dispose', tag: _tag);
    super.dispose();
  }



  /// 초기 데이터 로드 (한 번만 실행)
  Future<void> _loadInitialData() async {
    if (!mounted) return;

    LoggerUtils.methodStart('_loadInitialData', tag: _tag);

    // 2단계 가드: 현재 행사가 설정되지 않은 경우 데이터 로딩 시도하지 않음
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      LoggerUtils.logWarning('현재 행사가 설정되지 않아 데이터 로딩을 건너뜁니다', tag: _tag);
      LoggerUtils.methodEnd('_loadInitialData', tag: _tag);
      return;
    }

    final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
    if (currentEvent == null) {
      LoggerUtils.logWarning('행사 정보를 불러올 수 없어 데이터 로딩을 건너뜁니다', tag: _tag);
      LoggerUtils.methodEnd('_loadInitialData', tag: _tag);
      return;
    }

    // 초기 데이터 로딩 (실시간 동기화와 별개로 필요)
    LoggerUtils.logInfo('초기 데이터 로딩 시작', tag: _tag);

    try {
      // PrepaymentNotifier에서 데이터 로드
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();

      LoggerUtils.logInfo('초기 데이터 로딩 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('초기 데이터 로딩 실패', tag: _tag, error: e);
      // 실패해도 앱은 계속 실행
    }

    LoggerUtils.methodEnd('_loadInitialData', tag: _tag);
  }

  /// 검색 텍스트 변경 처리
  void _onSearchTextChanged(String query) {
    if (!mounted) return;

    LoggerUtils.methodStart('_onSearchTextChanged', tag: _tag);

    ErrorUtils.wrapError(
      context,
      () async {
        if (mounted) {
          await ref
              .read(prepaymentNotifierProvider.notifier)
              .searchPrepayments(query);
        }
      },
      errorMessage: '검색 중 오류가 발생했습니다',
      type: ErrorType.database,
      tag: _tag,
    );

    LoggerUtils.methodEnd('_onSearchTextChanged', tag: _tag);
  }

  /// 검색 모드 토글
  void _toggleSearchMode() {
    if (!mounted) return;

    LoggerUtils.methodStart('_toggleSearchMode', tag: _tag);

    setState(() {
      _isSearchMode = !_isSearchMode;
      if (!_isSearchMode) {
        _searchController.clear();
        if (mounted) {
          ref.read(prepaymentNotifierProvider.notifier).searchPrepayments('');
        }
      }
    });

    LoggerUtils.methodEnd('_toggleSearchMode', tag: _tag);
  }

  @override
  Widget build(BuildContext context) {
    // 현재 워크스페이스 변경 감지 및 데이터 새로고침
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (previous != null && next != null && previous.id != next.id) {
        LoggerUtils.logInfo('현재 이벤트 변경 감지: ${previous.name} -> ${next.name}', tag: _tag);
        _refreshDataAfterEventChange();
        // Consumer 위젯들이 자동으로 상태 변화를 감지하므로 수동 setState 불필요
      } else if (previous == null && next != null) {
        // 3단계 가드: 현재 행사가 null에서 설정된 경우 (온보딩 완료 후)
        LoggerUtils.logInfo('현재 이벤트 설정됨: ${next.name} - 데이터 새로고침', tag: _tag);
        _refreshDataAfterEventChange();
        // Consumer 위젯들이 자동으로 상태 변화를 감지하므로 수동 setState 불필요
      }
    });

    final workspaceState = ref.watch(unifiedWorkspaceProvider);

    final currentPrepaymentTab = ref.watch(prepaymentTabIndexProvider);

    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: _buildToolbarTitle(),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent, // Material 3에서 색상 덮어쓰기 방지
        elevation: 0,
        leadingWidth: currentPrepaymentTab == 0 ? 56 : null,
        leading: currentPrepaymentTab == 0
            ? IconButton(
                icon: Icon(_isSearchMode ? Icons.close : Icons.search),
                tooltip: _isSearchMode ? '검색 취소' : '검색',
                onPressed: _toggleSearchMode,
              )
            : null,
        actions: _buildAppBarActions(),
      ),
      // onDrawerChanged 제거 - 드로어 열 때마다 불필요한 프로필 이미지 체크 방지
      body: Stack(
        children: [
          SafeArea(
            bottom: false, // 하단 SafeArea 제거하여 네비게이션 바가 바닥에 붙도록
            child: const PrepaymentScreen(),
          ),
          // 워크스페이스 전환 로딩 오버레이
          if (workspaceState.isLoading) _buildWorkspaceLoadingOverlay(workspaceState),
        ],
      ),
    );
  }

  /// 툴바 타이틀
  Widget _buildToolbarTitle() {
    // 검색 모드인 경우 - 검색창 표시
    if (_isSearchMode) {
      return TextField(
        controller: _searchController,
        autofocus: true,
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', color: Colors.white),
        cursorColor: Colors.white,
        decoration: InputDecoration(
          filled: true,
          fillColor: Colors.black.withValues(alpha: 0.15),
          hintText: '선입금 검색...',
          hintStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', color: Colors.white70),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        ),
        onChanged: _onSearchTextChanged,
      );
    }

    // 일반 모드 - 단순 제목 표시
    return const Text(
      '선입금 관리',
      style: TextStyle(
        fontFamily: 'Pretendard',
        color: Colors.white,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  /// 요일 이름 반환 헬퍼 메서드
  String _getDayOfWeekName(int dayOfWeek) {
    switch (dayOfWeek) {
      case 1:
        return '월요일';
      case 2:
        return '화요일';
      case 3:
        return '수요일';
      case 4:
        return '목요일';
      case 5:
        return '금요일';
      case 6:
        return '토요일';
      case 7:
        return '일요일';
      default:
        return '전체';
    }
  }

  /// AppBar 액션들 빌드
  List<Widget> _buildAppBarActions() {
    final currentPrepaymentTab = ref.watch(prepaymentTabIndexProvider);

    // 탭 1 (상품 연동 관리)에서는 기어 메뉴만 표시
    if (currentPrepaymentTab == 1) {
      return [
        PopupMenuButton<_InventoryGearMenuAction>(
          icon: const Icon(Icons.settings),
          tooltip: '설정',
          position: PopupMenuPosition.under,
          offset: const Offset(0, 4),
          onSelected: (value) {
            switch (value) {
              case _InventoryGearMenuAction.prepaymentSettings:
                _showPrepaymentSettings();
                break;
              case _InventoryGearMenuAction.virtualProductManagement:
                _openPrepaymentVirtualProductManagement();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: _InventoryGearMenuAction.prepaymentSettings,
              child: Row(
                children: [
                  Icon(Icons.settings, size: 18, color: Theme.of(context).colorScheme.primary),
                  const SizedBox(width: 8),
                  const Text('선입금 설정', style: TextStyle(fontFamily: 'Pretendard')),
                ],
              ),
            ),
            PopupMenuItem(
              value: _InventoryGearMenuAction.virtualProductManagement,
              child: Row(
                children: [
                  Icon(Icons.inventory_2, size: 18, color: Theme.of(context).colorScheme.primary),
                  const SizedBox(width: 8),
                  const Text('선입금 상품 관리', style: TextStyle(fontFamily: 'Pretendard')),
                ],
              ),
            ),
          ],
        ),
      ];
    }

    // 기본 (선입금 목록 등) - 검색 버튼은 leading으로 이동, + 드롭다운 추가
    return [
      PopupMenuButton<String>(
        icon: const Icon(Icons.add),
        tooltip: '선입금 등록',
        position: PopupMenuPosition.under,
        offset: const Offset(0, 4),
        onSelected: (value) {
          switch (value) {
            case 'register_prepayment':
              _openRegisterPrepayment();
              break;
            case 'excel_prepayment':
              _openExcelPrepaymentImport();
              break;
          }
        },
        itemBuilder: (context) => [
          PopupMenuItem(
            value: 'register_prepayment',
            child: Row(
              children: [
                Icon(Icons.payment, size: 18, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('선입금 등록', style: TextStyle(fontFamily: 'Pretendard')),
              ],
            ),
          ),
          PopupMenuItem(
            value: 'excel_prepayment',
            child: Row(
              children: [
                Icon(Icons.download, size: 18, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text('엑셀로 선입금 등록', style: TextStyle(fontFamily: 'Pretendard')),
              ],
            ),
          ),
        ],
      ),
      IconButton(
        icon: const Icon(Icons.qr_code_scanner),
        onPressed: _openQrScanScreen,
        tooltip: 'QR코드 스캔',
      ),
      if (!_isSearchMode)
        IconButton(
          icon: const Icon(Icons.sort),
          onPressed: _showPrepaymentSortDialog,
          tooltip: '정렬',
        ),
    ];
  }


  /// 기어 메뉴: 선입금 설정 다이얼로그 표시
  Future<void> _showPrepaymentSettings() async {
    await showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          return ref.watch(settingsNotifierProvider).when(
            data: (state) => _InventoryPrepaymentSettingsDialog(
              collectDayOfWeekFromExcel: state.collectDayOfWeekFromExcel,
              excelDayOfWeekColumnIndex: state.excelDayOfWeekColumnIndex,
              linkPrepaymentToInventory: state.linkPrepaymentToInventory,
              onCollectDayOfWeekChanged: (value) async {
                await ref.read(settingsNotifierProvider.notifier).setCollectDayOfWeekFromExcel(value);
              },
              onColumnIndexChanged: (value) async {
                await ref.read(settingsNotifierProvider.notifier).setExcelDayOfWeekColumnIndex(value);
              },
              onLinkInventoryChanged: (value) async {
                await ref.read(settingsNotifierProvider.notifier).setLinkPrepaymentToInventory(value);
              },
            ),
            loading: () => const AlertDialog(
              title: Text('선입금 설정'),
              content: SizedBox(height: 80, child: Center(child: CircularProgressIndicator())),
            ),
            error: (error, stack) => AlertDialog(
              title: const Text('선입금 설정'),
              content: Text('설정을 불러오는 중 오류가 발생했습니다: ${error.toString()}'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('확인'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 기어 메뉴: 선입금 상품 관리 화면
  void _openPrepaymentVirtualProductManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const PrepaymentVirtualProductManagementScreen(),
      ),
    );
  }

  /// 선입금 단건 등록 화면 이동
  void _openRegisterPrepayment() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const RegisterPrepaymentScreen(),
      ),
    );
  }

  /// 엑셀로 선입금 등록 플로우 실행
  Future<void> _openExcelPrepaymentImport() async {
    // 엑셀 내보내기 기능 접근 권한 확인 (플러스 플랜 이상)
    final hasAccess = await SubscriptionUtils.checkFeatureAccess(
      ref: ref,
      context: context,
      featureName: 'excelExport',
    );

    if (hasAccess) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const ExcelImportScreen(),
        ),
      );
    }
  }







  /// 선입금 정렬 및 필터 다이얼로그 표시
  void _showPrepaymentSortDialog() {
    final currentOrder = ref.watch(prepaymentSortOrderProvider);
    final currentState = ref.watch(prepaymentNotifierProvider);
    final selectedDay = ref.watch(prepaymentDayOfWeekFilterProvider);
    final availableDays = ref.watch(prepaymentAvailableDaysOfWeekProvider);
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    showDialog(
      context: context,
      builder: (context) => custom_dialog.DialogTheme.buildModernDialog(
        isCompact: true,
        child: Padding(
          padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 제목
              Row(
                children: [
                  Icon(
                    Icons.sort,
                    size: isTablet ? 20 : 18,
                    color: AppColors.primarySeed,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '정렬 및 필터',
                      style: custom_dialog.DialogTheme.titleStyle.copyWith(
                        fontSize: isTablet ? 20.0 : 18.0,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

              // 내용
              Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.6,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 요일 필터 섹션
                      Row(
                        children: [
                          Text(
                            '요일 필터',
                            style: TextStyle(
                              fontSize: isTablet ? 16 : 14,
                              fontWeight: FontWeight.w600,
                              color: AppColors.onSurface,
                              fontFamily: 'Pretendard',
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton2<int>(
                                value: selectedDay,
                                isExpanded: true,
                                hint: Text(
                                  '요일 선택',
                                  style: TextStyle(
                                    fontFamily: 'Pretendard',
                                    fontSize: isTablet ? 16 : 14,
                                    color: AppColors.onSurfaceVariant,
                                  ),
                                ),
                                style: TextStyle(
                                  fontFamily: 'Pretendard',
                                  fontSize: isTablet ? 16 : 14,
                                  color: AppColors.onSurface,
                                ),
                                iconStyleData: IconStyleData(
                                  icon: Icon(
                                    Icons.arrow_drop_down,
                                    color: AppColors.onSurfaceVariant,
                                  ),
                                ),
                                buttonStyleData: ButtonStyleData(
                                  height: isTablet ? 50 : 46,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: isTablet ? 16 : 12,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.onSurfaceVariant),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                dropdownStyleData: DropdownStyleData(
                                  maxHeight: 200,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.1),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                ),
                                menuItemStyleData: MenuItemStyleData(
                                  height: isTablet ? 50 : 46,
                                  padding: EdgeInsets.symmetric(horizontal: isTablet ? 16 : 12),
                                ),
                                items: () {
                                  List<DropdownMenuItem<int>> items = [
                                    DropdownMenuItem<int>(
                                      value: 0,
                                      child: Text(
                                        '전체',
                                        style: TextStyle(
                                          fontFamily: 'Pretendard',
                                          fontSize: isTablet ? 16 : 14,
                                          color: AppColors.onSurface,
                                        ),
                                      ),
                                    ),
                                  ];

                                  if (availableDays.isNotEmpty) {
                                    for (int i = 1; i <= 7; i++) {
                                      if (availableDays.contains(i)) {
                                        items.add(DropdownMenuItem<int>(
                                          value: i,
                                          child: Text(
                                            _getDayOfWeekName(i),
                                            style: TextStyle(
                                              fontFamily: 'Pretendard',
                                              fontSize: isTablet ? 16 : 14,
                                              color: AppColors.onSurface,
                                            ),
                                          ),
                                        ));
                                      }
                                    }

                                    if (availableDays.contains(8)) {
                                      items.add(DropdownMenuItem<int>(
                                        value: 8,
                                        child: Text(
                                          '없음',
                                          style: TextStyle(
                                            fontFamily: 'Pretendard',
                                            fontSize: isTablet ? 16 : 14,
                                            color: AppColors.onSurface,
                                          ),
                                        ),
                                      ));
                                    }
                                  }

                                  return items;
                                }(),
                                onChanged: (value) {
                                  if (value != null) {
                                    ref.read(prepaymentDayOfWeekFilterProvider.notifier).state = value;
                                    ref.read(prepaymentNotifierProvider.notifier).filterByDayOfWeek(value);
                                    Navigator.pop(context);
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

                      // 수령 상태 필터 섹션
                      Row(
                        children: [
                          Text(
                            '수령 상태',
                            style: TextStyle(
                              fontSize: isTablet ? 16 : 14,
                              fontWeight: FontWeight.w600,
                              color: AppColors.onSurface,
                              fontFamily: 'Pretendard',
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton2<PrepaymentFilter>(
                                value: currentState.filter,
                                isExpanded: true,
                                hint: Text(
                                  '상태 선택',
                                  style: TextStyle(
                                    fontFamily: 'Pretendard',
                                    fontSize: isTablet ? 16 : 14,
                                    color: AppColors.onSurfaceVariant,
                                  ),
                                ),
                                style: TextStyle(
                                  fontFamily: 'Pretendard',
                                  fontSize: isTablet ? 16 : 14,
                                  color: AppColors.onSurface,
                                ),
                                iconStyleData: IconStyleData(
                                  icon: Icon(
                                    Icons.arrow_drop_down,
                                    color: AppColors.onSurfaceVariant,
                                  ),
                                ),
                                buttonStyleData: ButtonStyleData(
                                  height: isTablet ? 50 : 46,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: isTablet ? 16 : 12,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.onSurfaceVariant),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                dropdownStyleData: DropdownStyleData(
                                  maxHeight: 200,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.1),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                ),
                                menuItemStyleData: MenuItemStyleData(
                                  height: isTablet ? 50 : 46,
                                  padding: EdgeInsets.symmetric(horizontal: isTablet ? 16 : 12),
                                ),
                                items: PrepaymentFilter.values.map((filter) => DropdownMenuItem<PrepaymentFilter>(
                                  value: filter,
                                  child: Text(
                                    filter.displayName,
                                    style: TextStyle(
                                      fontFamily: 'Pretendard',
                                      fontSize: isTablet ? 16 : 14,
                                      color: AppColors.onSurface,
                                    ),
                                  ),
                                )).toList(),
                                onChanged: (filter) {
                                  if (filter != null) {
                                    ref.read(prepaymentNotifierProvider.notifier).setFilter(filter);
                                    Navigator.pop(context);
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

                      // 정렬 섹션
                      Text(
                        '정렬',
                        style: TextStyle(
                          fontSize: isTablet ? 16 : 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.onSurface,
                          fontFamily: 'Pretendard',
                        ),
                      ),
                      const SizedBox(height: 8),
                      ModernDialogComponents.buildPOSSelectionTile(
                        context: context,
                        title: '등록순',
                        subtitle: (currentOrder == PrepaymentSortOrder.registrationDateDesc || currentOrder == PrepaymentSortOrder.registrationDateAsc)
                            ? (currentOrder == PrepaymentSortOrder.registrationDateDesc ? '최신순' : '오래된순')
                            : null,
                        isSelected: currentOrder == PrepaymentSortOrder.registrationDateDesc || currentOrder == PrepaymentSortOrder.registrationDateAsc,
                        isTablet: isTablet,
                        trailing: (currentOrder == PrepaymentSortOrder.registrationDateDesc || currentOrder == PrepaymentSortOrder.registrationDateAsc)
                            ? Icon(
                                currentOrder == PrepaymentSortOrder.registrationDateDesc ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_up,
                                color: AppColors.primarySeed,
                                size: isTablet ? 20 : 18,
                              )
                            : null,
                        onTap: () {
                          final newOrder = currentOrder == PrepaymentSortOrder.registrationDateDesc
                              ? PrepaymentSortOrder.registrationDateAsc
                              : PrepaymentSortOrder.registrationDateDesc;
                          ref.read(prepaymentSortOrderProvider.notifier).state = newOrder;
                          ref.read(prepaymentNotifierProvider.notifier).loadPrepaymentsSorted(newOrder);
                          Navigator.pop(context);
                        },
                      ),
                      ModernDialogComponents.buildPOSSelectionTile(
                        context: context,
                        title: '이름순',
                        subtitle: currentOrder.isNameSort
                            ? (currentOrder.isAscending ? '오름차순' : '내림차순')
                            : null,
                        isSelected: currentOrder.isNameSort,
                        isTablet: isTablet,
                        trailing: currentOrder.isNameSort
                            ? Icon(
                                currentOrder.isAscending ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                                color: AppColors.primarySeed,
                                size: isTablet ? 20 : 18,
                              )
                            : null,
                        onTap: () {
                          final newOrder = currentOrder.isNameSort
                              ? currentOrder.toggleNameSort()
                              : PrepaymentSortOrder.buyerNameAsc;
                          ref.read(prepaymentSortOrderProvider.notifier).state = newOrder;
                          ref.read(prepaymentNotifierProvider.notifier).loadPrepaymentsSorted(newOrder);
                          Navigator.pop(context);
                        },
                      ),
                      ModernDialogComponents.buildPOSSelectionTile(
                        context: context,
                        title: '선입금 번호순',
                        subtitle: (currentOrder == PrepaymentSortOrder.prepaymentNumberAsc || currentOrder == PrepaymentSortOrder.prepaymentNumberDesc)
                            ? (currentOrder == PrepaymentSortOrder.prepaymentNumberAsc ? '오름차순' : '내림차순')
                            : null,
                        isSelected: currentOrder == PrepaymentSortOrder.prepaymentNumberAsc || currentOrder == PrepaymentSortOrder.prepaymentNumberDesc,
                        isTablet: isTablet,
                        trailing: (currentOrder == PrepaymentSortOrder.prepaymentNumberAsc || currentOrder == PrepaymentSortOrder.prepaymentNumberDesc)
                            ? Icon(
                                currentOrder == PrepaymentSortOrder.prepaymentNumberAsc ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                                color: AppColors.primarySeed,
                                size: isTablet ? 20 : 18,
                              )
                            : null,
                        onTap: () {
                          final newOrder = currentOrder == PrepaymentSortOrder.prepaymentNumberAsc
                              ? PrepaymentSortOrder.prepaymentNumberDesc
                              : PrepaymentSortOrder.prepaymentNumberAsc;
                          ref.read(prepaymentSortOrderProvider.notifier).state = newOrder;
                          ref.read(prepaymentNotifierProvider.notifier).loadPrepaymentsSorted(newOrder);
                          Navigator.pop(context);
                        },
                      ),
                      ModernDialogComponents.buildPOSSelectionTile(
                        context: context,
                        title: '금액순',
                        subtitle: (currentOrder == PrepaymentSortOrder.amountAsc || currentOrder == PrepaymentSortOrder.amountDesc)
                            ? (currentOrder == PrepaymentSortOrder.amountAsc ? '오름차순' : '내림차순')
                            : null,
                        isSelected: currentOrder == PrepaymentSortOrder.amountAsc || currentOrder == PrepaymentSortOrder.amountDesc,
                        isTablet: isTablet,
                        trailing: (currentOrder == PrepaymentSortOrder.amountAsc || currentOrder == PrepaymentSortOrder.amountDesc)
                            ? Icon(
                                currentOrder == PrepaymentSortOrder.amountAsc ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                                color: AppColors.primarySeed,
                                size: isTablet ? 20 : 18,
                              )
                            : null,
                        onTap: () {
                          final newOrder = currentOrder == PrepaymentSortOrder.amountAsc
                              ? PrepaymentSortOrder.amountDesc
                              : PrepaymentSortOrder.amountAsc;
                          ref.read(prepaymentSortOrderProvider.notifier).state = newOrder;
                          ref.read(prepaymentNotifierProvider.notifier).loadPrepaymentsSorted(newOrder);
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }



  void _openQrScanScreen() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const QrScanScreen(),
      ),
    );
    if (result is String && result.isNotEmpty) {
      // QR코드 결과가 URL이면 웹사이트로 이동
      final uri = Uri.tryParse(result);
      if (uri != null && (uri.isScheme('http') || uri.isScheme('https'))) {
        try {
          final canLaunch = await canLaunchUrl(uri);
          if (canLaunch) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
          } else {
            // canLaunchUrl이 false를 반환해도 실제로는 열릴 수 있으므로 시도
            await launchUrl(uri, mode: LaunchMode.externalApplication);
          }
        } catch (e) {
          ToastUtils.showError(context, 'URL을 열 수 없습니다: $result');
        }
      } else {
        ToastUtils.showInfo(context, 'QR코드 결과: $result');
      }
    }
  }

  /// 워크스페이스 전환 로딩 오버레이
  Widget _buildWorkspaceLoadingOverlay(UnifiedWorkspaceState workspaceState) {
    return Container(
      color: Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: Card(
          margin: const EdgeInsets.all(32),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  workspaceState.loadingMessage ?? '행사 전환 중...',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (workspaceState.errorMessage != null) ...[
                  const SizedBox(height: 12),
                  Text(
                    workspaceState.errorMessage!,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 인벤토리 화면 AppBar 기어 메뉴 항목
enum _InventoryGearMenuAction { prepaymentSettings, virtualProductManagement }

/// 인벤토리 내부에서 사용하는 선입금 설정 다이얼로그 (SettingsScreen과 별도 경량 버전)
class _InventoryPrepaymentSettingsDialog extends StatefulWidget {
  final bool collectDayOfWeekFromExcel;
  final int excelDayOfWeekColumnIndex;
  final bool linkPrepaymentToInventory;
  final ValueChanged<bool> onCollectDayOfWeekChanged;
  final ValueChanged<int> onColumnIndexChanged;
  final ValueChanged<bool> onLinkInventoryChanged;

  const _InventoryPrepaymentSettingsDialog({
    required this.collectDayOfWeekFromExcel,
    required this.excelDayOfWeekColumnIndex,
    required this.linkPrepaymentToInventory,
    required this.onCollectDayOfWeekChanged,
    required this.onColumnIndexChanged,
    required this.onLinkInventoryChanged,
  });

  @override
  State<_InventoryPrepaymentSettingsDialog> createState() => _InventoryPrepaymentSettingsDialogState();
}

class _InventoryPrepaymentSettingsDialogState extends State<_InventoryPrepaymentSettingsDialog> {
  late TextEditingController _dayOfWeekController;
  late bool _linkPrepaymentToInventory;

  String _indexToColumnLetter(int index) {
    if (index < 0) return '';
    String result = '';
    while (index >= 0) {
      result = String.fromCharCode(65 + (index % 26)) + result;
      index = (index ~/ 26) - 1;
    }
    return result;
  }


  @override
  void initState() {
    super.initState();
    _linkPrepaymentToInventory = widget.linkPrepaymentToInventory;
    _dayOfWeekController = TextEditingController(
      text: widget.excelDayOfWeekColumnIndex >= 0 ? _indexToColumnLetter(widget.excelDayOfWeekColumnIndex) : '',
    );
  }

  @override
  void dispose() {
    _dayOfWeekController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    return custom_dialog.DialogTheme.buildModernDialog(
      isCompact: true,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                custom_dialog.DialogTheme.buildCompactIconContainer(
                  icon: Icons.account_balance_wallet_rounded,
                  color: AppColors.onboardingAccent,
                  isTablet: isTablet,
                ),
                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                Expanded(
                  child: Text(
                    '선입금 재고 연동 설정',
                    style: custom_dialog.DialogTheme.titleStyle.copyWith(
                      fontSize: isTablet ? 20.0 : 18.0,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),
           
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),
            Container(
              padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.surfaceVariant,
                    AppColors.secondary.withValues(alpha: 0.3),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isTablet ? 8.0 : 6.0),
                    decoration: BoxDecoration(
                      gradient: _linkPrepaymentToInventory
                          ? LinearGradient(colors: [AppColors.success, AppColors.successLight])
                          : LinearGradient(colors: [AppColors.secondary, AppColors.secondaryLight]),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.link_rounded,
                      color: _linkPrepaymentToInventory
                          ? AppColors.onboardingTextOnPrimary
                          : AppColors.onboardingTextSecondary,
                      size: isTablet ? 20.0 : 16.0,
                    ),
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '재고 연동',
                          style: TextStyle(
                            fontSize: isTablet ? 16.0 : 14.0,
                            fontWeight: FontWeight.w600,
                            color: AppColors.onboardingTextPrimary,
                          ),
                        ),
                        Text(
                          '선입금 수령 시 재고 연동',
                          style: TextStyle(
                            fontSize: isTablet ? 14.0 : 12.0,
                            color: AppColors.onboardingTextSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: _linkPrepaymentToInventory,
                    onChanged: (value) => setState(() => _linkPrepaymentToInventory = value),
                    activeThumbColor: AppColors.success,
                    activeTrackColor: AppColors.success.withValues(alpha: 0.3),
                    inactiveThumbColor: AppColors.secondary,
                    inactiveTrackColor: AppColors.secondary.withValues(alpha: 0.3),
                  ),
                ],
              ),
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet) * 1.5),
            Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.secondary),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            vertical: isTablet ? 14.0 : 12.0,
                          ),
                          child: Text(
                            '취소',
                            style: TextStyle(
                              color: AppColors.onboardingTextSecondary,
                              fontSize: isTablet ? 16.0 : 14.0,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                Expanded(
                  child: custom_dialog.DialogTheme.buildGradientButton(
                    decoration: custom_dialog.DialogTheme.confirmButtonDecoration,
                    isCompact: true,
                    onPressed: () {
                      widget.onLinkInventoryChanged(_linkPrepaymentToInventory);
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      '확인',
                      style: TextStyle(
                        color: AppColors.onboardingTextOnPrimary,
                        fontSize: isTablet ? 16.0 : 14.0,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// DrawerHeader 대체: 로그인한 사용자의 닉네임을 Firestore에서 직접 불러와 표시






